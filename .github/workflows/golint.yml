name: Go lint
on:
  push:
    paths:
      - backend/**
  workflow_dispatch:

jobs:
  lint:
    name: lint
    runs-on: ubuntu-latest
    timeout-minutes: 5
    steps:
      - name: Check out code into the Go module directory
        uses: actions/checkout@v5
        with:
          submodules: "true"

      - name: Set up Go
        uses: actions/setup-go@v6
        with:
          go-version: "1.21"
        id: go

      
      - name: Run golangci-lint
        uses: golangci/golangci-lint-action@v8
        with:
          working-directory: backend
          args: --config=.golangci.yml
