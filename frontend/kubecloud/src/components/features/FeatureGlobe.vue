<template>
  <div
    ref="globeContainer"
    class="globe-canvas"
    :style="{ width: width + 'px', height: height + 'px', maxWidth: '100%' }"
  >
    <div ref="tooltipEl" class="globe-tooltip" style="display:none;"></div>

    <slot />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onBeforeUnmount, watch } from 'vue'
import * as THREE from 'three'

/**
 * Props for FeatureGlobe
 * @prop {number} width - Globe canvas width in px
 * @prop {number} height - Globe canvas height in px
 * @prop {number} pointCount - Number of points to render (default: 2500)
 * @prop {[number, number][]} nodes - Optional array of [lat, lng] for node positions
 * @prop {Array<{from: [number, number], to: [number, number]}>} arcs - Optional array of arcs
 */
const props = defineProps({
  width: { type: Number, default: 480 },
  height: { type: Number, default: 480 },
  pointCount: { type: Number, default: 2500 },
  nodes: { type: Array as () => [number, number][], default: undefined },
  arcs: { type: Array as () => Array<{from: [number, number], to: [number, number]}>, default: undefined },
  labels: { type: Array as () => string[] | undefined, default: undefined },
})

const emit = defineEmits(['node-click', 'node-hover', 'arc-hover'])

const globeContainer = ref<HTMLElement | null>(null)
const tooltipEl = ref<HTMLElement | null>(null)
let renderer: THREE.WebGLRenderer | null = null
let scene: THREE.Scene | null = null
let camera: THREE.PerspectiveCamera | null = null
let animationId: number | null = null
let pointCloud: THREE.Points | null = null
let raycaster: THREE.Raycaster | null = null
let mouse = new THREE.Vector2()
let INTERSECTED: number | null = null
let isDragging = false
let lastMouseX = 0
let lastMouseY = 0
let rotationY = 0
let rotationX = 0
let autoRotate = true

const GLOBE_RADIUS = 3.2

// Land alpha texture (white=land, black=water). Using a lightweight equirect map.
let landAlphaData: ImageData | null = null
// Longitude mapping variant for the alpha texture
// Labels for nodes, index-aligned with rendered actual nodes
let nodeLabels: string[] = []
async function loadLandMask(): Promise<void> {
  try {
    await loadRasterFromWorldAtlas(1024)
  } catch (_e) {
    // Fallback: tiny pre-baked alpha PNG
    await new Promise<void>((resolve) => {
      const img = new Image()
      img.crossOrigin = 'anonymous'
      img.src = 'https://unpkg.com/world-atlas-land@1.0.0/alpha-1024.png'
      img.onload = () => {
        const c = document.createElement('canvas')
        c.width = img.width
        c.height = img.height
        const ctx = c.getContext('2d')!
        ctx.drawImage(img, 0, 0)
        landAlphaData = ctx.getImageData(0, 0, img.width, img.height)
        resolve()
      }
      img.onerror = () => resolve()
    })
  }
}

// Fetch Natural Earth land TopoJSON (via world-atlas) and rasterize with d3-geo
async function loadRasterFromWorldAtlas(width: number = 1024): Promise<void> {
  // @ts-ignore - external URL import without types
  const d3 = await import(/* @vite-ignore */ 'https://cdn.jsdelivr.net/npm/d3-geo@3/+esm') as any
  // @ts-ignore - external URL import without types
  const topojson = await import(/* @vite-ignore */ 'https://cdn.jsdelivr.net/npm/topojson-client@3/+esm') as any
  const topoUrl = 'https://cdn.jsdelivr.net/npm/world-atlas@2/land-110m.json'
  const resp = await fetch(topoUrl)
  if (!resp.ok) throw new Error('Failed to fetch world-atlas land')
  const topo = await resp.json()
  const land = topojson.feature(topo, topo.objects.land)

  const aspect = 2 // equirectangular 2:1
  const w = width
  const h = Math.round(width / aspect)
  const canvas = document.createElement('canvas')
  canvas.width = w
  canvas.height = h
  const ctx = canvas.getContext('2d')!
  ctx.fillStyle = '#000'
  ctx.fillRect(0, 0, w, h)
  ctx.fillStyle = '#fff'

  const projection = d3.geoEquirectangular().fitSize([w, h], land)
  const path = d3.geoPath(projection, ctx as unknown as CanvasRenderingContext2D)
  ctx.beginPath()
  path(land as any)
  ctx.fill()
  landAlphaData = ctx.getImageData(0, 0, w, h)
}

// Extract coastline points directly from the alpha land mask
let cachedBorderPoints: [number, number][] | null = null
function getContinentBorders(): [number, number][] {
  if (cachedBorderPoints) return cachedBorderPoints
  if (!landAlphaData) return []
  const w = landAlphaData.width
  const h = landAlphaData.height
  const step = 3 // pixel cell size; balance detail and perf
  const threshold = 127
  const sample = (x: number, y: number) => {
    const ix = ((x % w + w) % w) + Math.max(0, Math.min(h - 1, y)) * w
    return landAlphaData!.data[ix * 4] > threshold ? 1 : 0
  }
  const points: [number, number][] = []
  const addPoint = (px: number, py: number) => {
    const lng = (px / w) * 360 - 180
    const lat = 90 - (py / h) * 180
    points.push([lat, lng])
  }
  // marching squares edge interpolation helper
  const interp = (x1: number, y1: number, x2: number, y2: number, v1: number, v2: number) => {
    const t = 0.5 // simple midpoint (binary mask), good enough for our halftone
    return { x: x1 + (x2 - x1) * t, y: y1 + (y2 - y1) * t }
  }
  for (let y = 0; y < h; y += step) {
    for (let x = 0; x < w; x += step) {
      const v0 = sample(x, y)
      const v1 = sample(x + step, y)
      const v2 = sample(x + step, y + step)
      const v3 = sample(x, y + step)
      const idx = (v0 << 3) | (v1 << 2) | (v2 << 1) | v3
      if (idx === 0 || idx === 15) continue
      const x0 = x, y0 = y, x1p = x + step, y1p = y + step
      // edges: a=top(x0->x1p,y0), b=right(x1p,y0->y1p), c=bottom(x1p->x0,y1p), d=left(x0,y1p->y0)
      const a = interp(x0, y0, x1p, y0, v0, v1)
      const b = interp(x1p, y0, x1p, y1p, v1, v2)
      const c = interp(x1p, y1p, x0, y1p, v2, v3)
      const d = interp(x0, y1p, x0, y0, v3, v0)
      // handle cases; add a few samples along each segment for curvature look
      switch (idx) {
        case 1: case 14: addPoint((d.x + c.x) * 0.5, (d.y + c.y) * 0.5); break
        case 2: case 13: addPoint((b.x + c.x) * 0.5, (b.y + c.y) * 0.5); break
        case 3: case 12: addPoint((a.x + c.x) * 0.5, (a.y + c.y) * 0.5); break
        case 4: case 11: addPoint((a.x + b.x) * 0.5, (a.y + b.y) * 0.5); break
        case 5: case 10: addPoint((a.x + d.x) * 0.5, (a.y + d.y) * 0.5); addPoint((b.x + c.x) * 0.5, (b.y + c.y) * 0.5); break
        case 6: case 9: addPoint((b.x + d.x) * 0.5, (b.y + d.y) * 0.5); break
        case 7: case 8: addPoint((a.x + d.x) * 0.5, (a.y + d.y) * 0.5); break
      }
    }
  }
  cachedBorderPoints = points
  return points
}

function getActualNodes(): [number, number][] {
  try {
    if (props.nodes && Array.isArray(props.nodes) && props.nodes.length > 0) {
      return props.nodes.filter(([lat, lng]) => typeof lat === 'number' && typeof lng === 'number' && !isNaN(lat) && !isNaN(lng) && Math.abs(lat) <= 90 && Math.abs(lng) <= 180)
    }
  } catch (_) {}
  return []
}


// Generate points directly from the alpha land mask (most accurate)
function generateLandDotsFromMask(stride: number = 3): [number, number][] {
  const points: [number, number][] = []
  if (!landAlphaData) return points
  const w = landAlphaData.width
  const h = landAlphaData.height
  const threshold = 127
  for (let y = 0; y < h; y += stride) {
    for (let x = 0; x < w; x += stride) {
      const idx = (x + y * w) * 4
      if (landAlphaData.data[idx] > threshold) {
        const lng = (x / w) * 360 - 180
        const lat = 90 - (y / h) * 180
        points.push([lat, lng])
      }
    }
  }
  return points
}

function createGlobe() {
  if (!globeContainer.value || !renderer) {
    console.error('Missing container or renderer for globe creation')
    return
  }

  scene = new THREE.Scene()
  scene.background = null
  camera = new THREE.PerspectiveCamera(75, props.width / props.height, 0.1, 1000)
  camera.position.set(0, 0, 9) // Move camera back for better globe size
  camera.lookAt(0, 0, 0)

  // Opaque base sphere to occlude back-side points (fixes hollow look)
  const baseSphereGeometry = new THREE.SphereGeometry(GLOBE_RADIUS, 64, 64)
  const baseSphereMaterial = new THREE.MeshBasicMaterial({ color: 0x0f1e3a })
  const baseSphere = new THREE.Mesh(baseSphereGeometry, baseSphereMaterial)
  scene.add(baseSphere)

  const actualNodes = getActualNodes()

  // Always create background points to ensure globe is visible
  const bgGeometry = new THREE.BufferGeometry()
  const landPoints = landAlphaData ? generateLandDotsFromMask(3) : []
  const bgPositions = new Float32Array(landPoints.length * 3)
  const bgColors = new Float32Array(landPoints.length * 3)

  // Get continent border points for different coloring
  const continentBorders = getContinentBorders()
  const borderSet = new Set(continentBorders.map(p => `${p[0]},${p[1]}`))

  for (let i = 0; i < landPoints.length; i++) {
    const [lat, lng] = landPoints[i]
    const phi = (90 - lat) * (Math.PI / 180)
    const theta = (lng + 180) * (Math.PI / 180)
    const x = GLOBE_RADIUS * Math.sin(phi) * Math.cos(theta)
    const y = GLOBE_RADIUS * Math.cos(phi)
    const z = GLOBE_RADIUS * Math.sin(phi) * Math.sin(theta)
    bgPositions[i * 3] = x
    bgPositions[i * 3 + 1] = y
    bgPositions[i * 3 + 2] = z

     // Enhanced colors for continent borders vs ocean points
     const isBorder = borderSet.has(`${lat},${lng}`)
     if (isBorder) {
       // Bright white/blue for continent borders - more prominent
       bgColors[i * 3] = 1.0     // r - maximum brightness
       bgColors[i * 3 + 1] = 1.0 // g - maximum brightness
       bgColors[i * 3 + 2] = 1.0 // b - bright blue tint
     } else {
       // Enhanced blue gradient for ocean points - more visible
       const c = 0.6 + 0.3 * (y / GLOBE_RADIUS)
       bgColors[i * 3] = 0.3 * c + 0.15   // R: more visible
       bgColors[i * 3 + 1] = 0.5 * c + 0.25 // G: blue-green
       bgColors[i * 3 + 2] = 0.8 * c + 0.4  // B: bright blue
     }
  }

  bgGeometry.setAttribute('position', new THREE.BufferAttribute(bgPositions, 3))
  bgGeometry.setAttribute('color', new THREE.BufferAttribute(bgColors, 3))

   // Custom shader for circular background points
   const bgMaterial = new THREE.ShaderMaterial({
     uniforms: {},
     vertexShader: `
       attribute vec3 color;
       varying vec3 vColor;
       void main() {
         vColor = color;
         vec4 mvPosition = modelViewMatrix * vec4(position, 1.0);
         gl_PointSize = 1.6;
         gl_Position = projectionMatrix * mvPosition;
       }
     `,
     fragmentShader: `
       varying vec3 vColor;
       void main() {
         float distance = length(gl_PointCoord - vec2(0.5));
         if (distance > 0.5) discard;

         // Create circular shape with soft edges
         float alpha = 1.0 - (distance * 2.0);
         alpha = pow(alpha, 1.0);

         // Add subtle glow to background points
         vec3 glowColor = vColor * 0.15;
         vec3 finalColor = vColor + glowColor;
         finalColor = min(finalColor, vec3(1.0));
         
         gl_FragColor = vec4(finalColor, alpha * 1.0);
       }
     `,
     transparent: true,
     blending: THREE.AdditiveBlending
   })
  const bgPointCloud = new THREE.Points(bgGeometry, bgMaterial)
  scene.add(bgPointCloud)

  // Create actual nodes if available
  if (actualNodes.length > 0) {
    if (props.labels && props.labels.length === actualNodes.length) {
      nodeLabels = [...props.labels]
    } else {
      nodeLabels = actualNodes.map(([lat, lng]) => `(${lat.toFixed(2)}, ${lng.toFixed(2)})`)
    }
    const nodeGeometry = new THREE.BufferGeometry()
    const nodePositions = new Float32Array(actualNodes.length * 3)
    const nodeColors = new Float32Array(actualNodes.length * 3)
    const baseColors = new Float32Array(actualNodes.length * 3)

    for (let i = 0; i < actualNodes.length; i++) {
      const [lat, lng] = actualNodes[i]
      const phi = (90 - lat) * (Math.PI / 180)
      const theta = (lng + 180) * (Math.PI / 180)
      const x = GLOBE_RADIUS * Math.sin(phi) * Math.cos(theta)
      const y = GLOBE_RADIUS * Math.cos(phi)
      const z = GLOBE_RADIUS * Math.sin(phi) * Math.sin(theta)
      nodePositions[i * 3] = x
      nodePositions[i * 3 + 1] = y
      nodePositions[i * 3 + 2] = z

       // Simple warm orange color for nodes
       const brightness = 0.9 + 0.1 * (y / GLOBE_RADIUS)
       const r = 1.0 * brightness
       const g = 0.8 * brightness
       const b = 0.3 * brightness

      nodeColors[i * 3] = r
      nodeColors[i * 3 + 1] = g
      nodeColors[i * 3 + 2] = b
      baseColors[i * 3] = r
      baseColors[i * 3 + 1] = g
      baseColors[i * 3 + 2] = b
    }

    nodeGeometry.setAttribute('position', new THREE.BufferAttribute(nodePositions, 3))
    nodeGeometry.setAttribute('color', new THREE.BufferAttribute(nodeColors, 3))
    nodeGeometry.setAttribute('baseColor', new THREE.BufferAttribute(baseColors, 3))

     // Clean, professional nodes
     const nodeMaterial = new THREE.ShaderMaterial({
       uniforms: {},
       vertexShader: `
         precision mediump float;
         attribute vec3 color;
         varying vec3 vColor;
         void main() {
           vColor = color;
           vec3 pos = position + normalize(position) * 0.015;
           vec4 mvPosition = modelViewMatrix * vec4(pos, 1.0);
           float depth = -mvPosition.z;
           float size = 4.5;
           float atten = 100.0 / max(60.0, depth);
           gl_PointSize = clamp(size * atten, 3.0, 7.0);
           gl_Position = projectionMatrix * mvPosition;
         }
       `,
       fragmentShader: `
         precision mediump float;
         varying vec3 vColor;
         void main() {
           vec2 uv = gl_PointCoord - vec2(0.5);
           float r = length(uv);
           if (r > 0.5) discard;
           float alpha = smoothstep(0.5, 0.3, r);
           float core = smoothstep(0.2, 0.0, r);
           vec3 color = vColor * 0.8 + vec3(1.0) * 0.2;
           float finalAlpha = alpha * 0.9 + core * 0.1;
           gl_FragColor = vec4(min(color, vec3(1.0)), finalAlpha);
         }
       `,
       transparent: true,
       blending: THREE.NormalBlending,
       depthWrite: true,
       depthTest: true
     })
    pointCloud = new THREE.Points(nodeGeometry, nodeMaterial)
    scene.add(pointCloud)
  } else {
    // Empty point cloud for interaction
    const emptyGeometry = new THREE.BufferGeometry()
    emptyGeometry.setAttribute('position', new THREE.BufferAttribute(new Float32Array(0), 3))
    emptyGeometry.setAttribute('color', new THREE.BufferAttribute(new Float32Array(0), 3))
    pointCloud = new THREE.Points(emptyGeometry, new THREE.PointsMaterial({ size: 0.08, vertexColors: true }))
    scene.add(pointCloud)
  }

  // Simple lighting setup
  const ambientLight = new THREE.AmbientLight('#ffffff', 0.8)
  scene.add(ambientLight)
  const directionalLight = new THREE.DirectionalLight('#ffffff', 0.6)
  directionalLight.position.set(5, 5, 5)
  scene.add(directionalLight)

  // Raycaster for hover/click
  raycaster = new THREE.Raycaster()
}

function animate() {
  if (!renderer || !scene || !camera) {
    console.error('Missing renderer, scene, or camera for animation')
    return
  }
  animationId = requestAnimationFrame(animate)

  // Auto-rotate unless dragging
  if (autoRotate && !isDragging) {
    rotationY += 0.002
  }

  // Rotate all objects in the scene and update time uniforms
  scene.children.forEach(child => {
    if (child instanceof THREE.Points) {
      child.rotation.y = rotationY
      child.rotation.x = rotationX
      
    }
  })

  // Hover effect (only on actual nodes)
  if (raycaster && pointCloud && globeContainer.value) {
    raycaster.setFromCamera(mouse, camera!)
    const intersects = raycaster.intersectObject(pointCloud)
    const geometry = pointCloud.geometry as THREE.BufferGeometry
    const colors = geometry.getAttribute('color') as THREE.BufferAttribute

    if (INTERSECTED !== null && colors) {
      // Restore previous color from base colors
      const baseColors = geometry.getAttribute('baseColor') as THREE.BufferAttribute
      if (baseColors) {
        colors.setX(INTERSECTED, baseColors.array[INTERSECTED * 3])
        colors.setY(INTERSECTED, baseColors.array[INTERSECTED * 3 + 1])
        colors.setZ(INTERSECTED, baseColors.array[INTERSECTED * 3 + 2])
      }
    }

    if (intersects.length > 0) {
      const index = intersects[0].index
      if (index !== undefined && colors) {
        INTERSECTED = index
        // Bright white highlight for hovered nodes
        colors.setX(index, 1.0)
        colors.setY(index, 1.0)
        colors.setZ(index, 1.0)
        emit('node-hover', index)

        if (tooltipEl.value) {
          const posAttr = geometry.getAttribute('position') as THREE.BufferAttribute
          const vx = posAttr.getX(index)
          const vy = posAttr.getY(index)
          const vz = posAttr.getZ(index)
          const vector = new THREE.Vector3(vx, vy, vz)
          vector.project(camera!)
          const rect = globeContainer.value.getBoundingClientRect()
          const sx = (vector.x * 0.5 + 0.5) * rect.width
          const sy = (-vector.y * 0.5 + 0.5) * rect.height
          const el = tooltipEl.value
          el.style.display = 'block'
          el.style.left = `${sx + 6}px`
          el.style.top = `${sy - 8}px`
          const label = nodeLabels[index] ? nodeLabels[index] : `Node ${index + 1}`
          el.innerText = `Node ID: ${label}`
        }
      }
    } else {
      INTERSECTED = null
      if (tooltipEl.value) tooltipEl.value.style.display = 'none'
    }

    if (colors) {
      colors.needsUpdate = true
    }
  }

  renderer.render(scene, camera)
}

function resizeRenderer() {
  if (!renderer || !camera || !globeContainer.value) return
  renderer.setSize(props.width, props.height, false)
  camera.aspect = props.width / props.height
  camera.updateProjectionMatrix()
}

function onPointerDown(event: MouseEvent) {
  isDragging = true
  autoRotate = false
  lastMouseX = event.clientX
  lastMouseY = event.clientY
}
function onPointerUp() {
  isDragging = false
  autoRotate = true
}
function onPointerMove(event: MouseEvent) {
  if (!globeContainer.value) return
  // Mouse for raycaster
  const rect = globeContainer.value.getBoundingClientRect()
  mouse.x = ((event.clientX - rect.left) / rect.width) * 2 - 1
  mouse.y = -((event.clientY - rect.top) / rect.height) * 2 + 1
  // Drag to rotate
  if (isDragging) {
    const deltaX = event.clientX - lastMouseX
    const deltaY = event.clientY - lastMouseY
    rotationY += deltaX * 0.01
    rotationX += deltaY * 0.01
    lastMouseX = event.clientX
    lastMouseY = event.clientY
  }
}
function onPointerLeave() {
  // Hide tooltip and clear hover state when leaving the canvas
  INTERSECTED = null
  if (tooltipEl.value) tooltipEl.value.style.display = 'none'
}
function onPointerClick(_event: MouseEvent) {
  if (INTERSECTED !== null && pointCloud) {
    emit('node-click', INTERSECTED)
    // Create a temporary bright flash effect for clicked nodes
    const geometry = pointCloud.geometry as THREE.BufferGeometry
    const colors = geometry.getAttribute('color') as THREE.BufferAttribute
    const baseColors = geometry.getAttribute('baseColor') as THREE.BufferAttribute

    if (colors && baseColors) {
      const i = INTERSECTED
      let t = 0
      function flash() {
        t += 0.1
        const intensity = Math.max(0, 1 - t) * 2 // Bright flash that fades
        colors.setX(i, Math.min(1, baseColors.array[i * 3] + intensity))
        colors.setY(i, Math.min(1, baseColors.array[i * 3 + 1] + intensity))
        colors.setZ(i, Math.min(1, baseColors.array[i * 3 + 2] + intensity))
        colors.needsUpdate = true

        if (t < 1) {
          requestAnimationFrame(flash)
        } else {
          // Restore original color
          colors.setX(i, baseColors.array[i * 3])
          colors.setY(i, baseColors.array[i * 3 + 1])
          colors.setZ(i, baseColors.array[i * 3 + 2])
          colors.needsUpdate = true
        }
      }
      flash()
    }
  }
}



onMounted(async () => {
  try {
    if (!globeContainer.value) {
      return
    }

    await loadLandMask()

    renderer = new THREE.WebGLRenderer({ antialias: true, alpha: true })
    renderer.setClearColor(0x000000, 0) // transparent
    renderer.setSize(props.width, props.height)

    globeContainer.value.appendChild(renderer.domElement)

    createGlobe()
    resizeRenderer()

    window.addEventListener('resize', resizeRenderer)
    globeContainer.value.addEventListener('pointerdown', onPointerDown)
    globeContainer.value.addEventListener('pointerup', onPointerUp)
    globeContainer.value.addEventListener('pointermove', onPointerMove)
    globeContainer.value.addEventListener('pointerleave', onPointerLeave)
    globeContainer.value.addEventListener('click', onPointerClick)

    animate()
  } catch (error) {
    console.error('Error in FeatureGlobe onMounted:', error)
  }
})

onBeforeUnmount(() => {
  if (animationId) {
    cancelAnimationFrame(animationId)
  }
  window.removeEventListener('resize', resizeRenderer)
  if (globeContainer.value) {
    globeContainer.value.removeEventListener('pointerdown', onPointerDown)
    globeContainer.value.removeEventListener('pointerup', onPointerUp)
    globeContainer.value.removeEventListener('pointermove', onPointerMove)
    globeContainer.value.removeEventListener('pointerleave', onPointerLeave)
    globeContainer.value.removeEventListener('click', onPointerClick)
  }
  if (renderer && globeContainer.value) {
    globeContainer.value.removeChild(renderer.domElement)
  }

  renderer = null
  scene = null
  camera = null
  pointCloud = null
  raycaster = null
})

// Watch for size changes only (nodes are handled separately)
watch(() => [props.width, props.height], (newVal, oldVal) => {
  if (oldVal && (newVal[0] !== oldVal[0] || newVal[1] !== oldVal[1])) {
    resizeRenderer()
  }
})

// Watch for nodes changes separately with debouncing
let nodeUpdateTimeout: number | null = null
watch(() => props.nodes, (newNodes, oldNodes) => {
  if (nodeUpdateTimeout) {
    clearTimeout(nodeUpdateTimeout)
  }

  nodeUpdateTimeout = window.setTimeout(() => {
    if (scene && renderer && camera && globeContainer.value) {
      const oldLength = Array.isArray(oldNodes) ? oldNodes.length : 0
      const newLength = Array.isArray(newNodes) ? newNodes.length : 0

      if (oldLength !== newLength) {
        // Remove old scene
        while (scene.children.length > 0) {
          scene.remove(scene.children[0])
        }
        createGlobe()
      }
    }
  }, 100) // Debounce for 100ms
}, { deep: true })
</script>

<style scoped>
.globe-canvas {
  min-width: 300px;
  min-height: 300px;
  margin: 0 auto;
  position: relative;
  z-index: 3;
  background: none;
  border-radius: 50%;
  cursor: grab;
  overflow: visible;
  display: flex;
  align-items: center;
  justify-content: center;
}

.globe-canvas canvas {
  display: block;
  margin: 0 auto;
}
.globe-canvas:active {
  cursor: grabbing;
}
.globe-tooltip {
  position: absolute;
  padding: 6px 8px;
  background: rgba(15, 23, 42, 0.9);
  border: 1px solid rgba(96, 165, 250, 0.4);
  color: #e5f0ff;
  font-size: 12px;
  border-radius: 6px;
  pointer-events: none;
  transform: translate(-50%, -100%);
  white-space: nowrap;
}
@media (max-width: 600px) {
  .globe-canvas {
    height: 400px;
    min-height: 300px;
  }
}
</style>
