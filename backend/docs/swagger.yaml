basePath: /api/v1
definitions:
  app.APIResponse:
    properties:
      data: {}
      error:
        type: string
      message:
        type: string
      status:
        type: integer
    type: object
  app.ChangePasswordInput:
    properties:
      confirm_password:
        type: string
      email:
        type: string
      password:
        maxLength: 64
        minLength: 8
        type: string
    required:
    - confirm_password
    - email
    - password
    type: object
  app.ChargeBalanceInput:
    properties:
      amount:
        type: number
      card_type:
        type: string
      payment_method_id:
        type: string
    required:
    - amount
    - card_type
    - payment_method_id
    type: object
  app.ChargeBalanceResponse:
    properties:
      email:
        type: string
      workflow_id:
        type: string
    type: object
  app.ClusterInput:
    properties:
      name:
        type: string
      nodes:
        items:
          $ref: '#/definitions/app.NodeInput'
        type: array
      token:
        type: string
    required:
    - name
    - nodes
    type: object
  app.CreditRequestInput:
    properties:
      amount:
        type: number
      memo:
        maxLength: 255
        minLength: 3
        type: string
    required:
    - amount
    - memo
    type: object
  app.CreditUserResponse:
    properties:
      amount:
        type: number
      memo:
        type: string
      user:
        type: string
    type: object
  app.DeploymentListResponse:
    properties:
      count:
        type: integer
      deployments:
        items:
          $ref: '#/definitions/app.DeploymentResponse'
        type: array
    type: object
  app.DeploymentResponse:
    properties:
      cluster: {}
      created_at:
        type: string
      id:
        type: integer
      project_name:
        type: string
      updated_at:
        type: string
    type: object
  app.EmailInput:
    properties:
      email:
        type: string
    required:
    - email
    type: object
  app.GenerateVouchersInput:
    properties:
      count:
        type: integer
      expire_after_days:
        type: integer
      value:
        type: number
    required:
    - count
    - expire_after_days
    - value
    type: object
  app.GetUserResponse:
    properties:
      account_address:
        type: string
      admin:
        type: boolean
      code:
        type: integer
      credit_card_balance:
        description: millicent, money from credit card
        type: integer
      credited_balance:
        description: millicent, manually added by admin or from vouchers
        type: integer
      debt:
        description: millicent
        type: integer
      email:
        type: string
      id:
        type: integer
      password:
        items:
          type: integer
        type: array
      pending_balance_usd:
        type: number
      sponsored:
        type: boolean
      ssh_key:
        type: string
      stripe_customer_id:
        type: string
      updated_at:
        type: string
      username:
        type: string
      verified:
        type: boolean
    required:
    - email
    - password
    - username
    type: object
  app.KubeconfigResponse:
    properties:
      kubeconfig:
        type: string
    type: object
  app.ListNodesWithDiscountResponse:
    properties:
      nodes:
        items:
          $ref: '#/definitions/app.NodesWithDiscount'
        type: array
      total:
        type: integer
    type: object
  app.LoginInput:
    properties:
      email:
        type: string
      password:
        maxLength: 64
        minLength: 3
        type: string
    required:
    - email
    - password
    type: object
  app.MaintenanceModeStatus:
    properties:
      enabled:
        type: boolean
    type: object
  app.NodeInput:
    properties:
      cpu:
        type: integer
      disk_size:
        description: Storage in MB
        type: integer
      entrypoint:
        type: string
      env_vars:
        additionalProperties:
          type: string
        description: SSH_KEY, etc.
        type: object
      flist:
        type: string
      gpu_ids:
        description: List of GPU IDs
        items:
          type: string
        type: array
      memory:
        description: Memory in MB
        type: integer
      name:
        type: string
      node_id:
        type: integer
      root_size:
        description: Storage in MB
        type: integer
      type:
        enum:
        - worker
        - master
        - leader
        type: string
    required:
    - cpu
    - memory
    - name
    - node_id
    - root_size
    - type
    type: object
  app.NodesWithDiscount:
    properties:
      discount_price:
        type: number
      node: {}
    type: object
  app.PendingRecordsResponse:
    properties:
      created_at:
        type: string
      id:
        type: integer
      tft_amount:
        description: TFTs are multiplied by 1e7
        type: integer
      transfer_mode:
        type: string
      transferred_tft_amount:
        type: integer
      transferred_usd_amount:
        type: number
      updated_at:
        type: string
      usd_amount:
        type: number
      user_id:
        type: integer
      username:
        type: string
    type: object
  app.RedeemVoucherResponse:
    properties:
      amount:
        type: number
      email:
        type: string
      voucher_code:
        type: string
      workflow_id:
        type: string
    type: object
  app.RefreshTokenInput:
    properties:
      refresh_token:
        type: string
    required:
    - refresh_token
    type: object
  app.RefreshTokenResponse:
    properties:
      access_token:
        type: string
    type: object
  app.RegisterInput:
    properties:
      confirm_password:
        type: string
      email:
        type: string
      name:
        maxLength: 64
        minLength: 3
        type: string
      password:
        maxLength: 64
        minLength: 8
        type: string
    required:
    - confirm_password
    - email
    - name
    - password
    type: object
  app.RegisterResponse:
    properties:
      email:
        type: string
      timeout:
        type: string
    type: object
  app.RegisterUserResponse:
    properties:
      email:
        type: string
      workflow_id:
        type: string
    type: object
  app.ReserveNodeResponse:
    properties:
      email:
        type: string
      node_id:
        type: integer
      workflow_id:
        type: string
    type: object
  app.Response:
    properties:
      message:
        type: string
      status:
        type: string
      task_id:
        type: string
    type: object
  app.SSHKeyInput:
    properties:
      name:
        type: string
      public_key:
        type: string
    required:
    - name
    - public_key
    type: object
  app.SendMailResponse:
    properties:
      failed_emails:
        items:
          type: string
        type: array
      failed_emails_count:
        type: integer
      successful_emails:
        type: integer
      total_users:
        type: integer
    type: object
  app.Stats:
    properties:
      countries:
        type: integer
      total_clusters:
        type: integer
      total_users:
        type: integer
      up_nodes:
        type: integer
    type: object
  app.TwinResponse:
    properties:
      account_id:
        type: string
      public_key:
        type: string
      relay:
        type: string
      twin_id:
        type: integer
    type: object
  app.UnreserveNodeResponse:
    properties:
      contract_id:
        type: integer
      email:
        type: string
      workflow_id:
        type: string
    type: object
  app.UserBalanceResponse:
    properties:
      balance_usd:
        type: number
      debt_usd:
        type: number
      pending_balance_usd:
        type: number
    type: object
  app.UserResponse:
    properties:
      account_address:
        type: string
      admin:
        type: boolean
      balance:
        description: USD balance
        type: number
      code:
        type: integer
      credit_card_balance:
        description: millicent, money from credit card
        type: integer
      credited_balance:
        description: millicent, manually added by admin or from vouchers
        type: integer
      debt:
        description: millicent
        type: integer
      email:
        type: string
      id:
        type: integer
      password:
        items:
          type: integer
        type: array
      sponsored:
        type: boolean
      ssh_key:
        type: string
      stripe_customer_id:
        type: string
      updated_at:
        type: string
      username:
        type: string
      verified:
        type: boolean
    required:
    - email
    - password
    - username
    type: object
  app.VerifyCodeInput:
    properties:
      code:
        type: integer
      email:
        type: string
    required:
    - code
    - email
    type: object
  app.VerifyRegisterUserResponse:
    properties:
      access_token:
        type: string
      email:
        type: string
      refresh_token:
        type: string
      workflow_id:
        type: string
    type: object
  internal.TokenPair:
    properties:
      access_token:
        type: string
      refresh_token:
        type: string
    type: object
  models.Invoice:
    properties:
      created_at:
        type: string
      id:
        type: integer
      nodes:
        items:
          $ref: '#/definitions/models.NodeItem'
        type: array
      tax:
        description: 'TODO:'
        type: number
      total:
        type: number
      user_id:
        type: integer
    required:
    - user_id
    type: object
  models.NodeItem:
    properties:
      contract_id:
        type: integer
      cost:
        type: number
      id:
        type: integer
      invoice_id:
        type: integer
      node_id:
        type: integer
      period:
        type: number
      rent_created_at:
        type: string
    type: object
  models.SSHKey:
    properties:
      created_at:
        type: string
      id:
        description: Primary key
        type: integer
      name:
        description: Unique name per user
        type: string
      public_key:
        description: Unique public key per user
        type: string
      updated_at:
        type: string
      userID:
        description: User owner
        type: integer
    required:
    - name
    - public_key
    - userID
    type: object
  models.Voucher:
    properties:
      code:
        type: string
      created_at:
        type: string
      expires_at:
        type: string
      id:
        type: integer
      redeemed:
        type: boolean
      value:
        type: number
    required:
    - code
    - created_at
    - expires_at
    - value
    type: object
info:
  contact: {}
  description: API documentation for KubeCloud.
  title: KubeCloud API
  version: "1.0"
paths:
  /deployments:
    delete:
      description: Deletes all deployments and their resources for the authenticated
        user
      produces:
      - application/json
      responses:
        "200":
          description: Delete all deployments workflow started successfully
          schema:
            $ref: '#/definitions/app.Response'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/app.APIResponse'
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/app.APIResponse'
      security:
      - BearerAuth: []
      summary: Delete all deployments
      tags:
      - deployments
    get:
      description: Retrieves a list of all deployments (clusters) for the authenticated
        user
      produces:
      - application/json
      responses:
        "200":
          description: Deployments retrieved successfully
          schema:
            $ref: '#/definitions/app.DeploymentListResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/app.APIResponse'
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/app.APIResponse'
      security:
      - BearerAuth: []
      summary: List deployments
      tags:
      - deployments
    post:
      consumes:
      - application/json
      description: Creates and deploys a new Kubernetes cluster
      parameters:
      - description: Cluster configuration
        in: body
        name: cluster
        required: true
        schema:
          $ref: '#/definitions/app.ClusterInput'
      produces:
      - application/json
      responses:
        "202":
          description: Deployment workflow started successfully
          schema:
            $ref: '#/definitions/app.Response'
        "400":
          description: Invalid request format
          schema:
            $ref: '#/definitions/app.APIResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/app.APIResponse'
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/app.APIResponse'
      security:
      - BearerAuth: []
      summary: Deploy cluster
      tags:
      - deployments
  /deployments/{name}:
    delete:
      description: Deletes a specific deployment and all its resources
      parameters:
      - description: Deployment name
        in: path
        name: name
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: Deployment deletion workflow started successfully
          schema:
            $ref: '#/definitions/app.Response'
        "400":
          description: Invalid request
          schema:
            $ref: '#/definitions/app.APIResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/app.APIResponse'
        "404":
          description: Deployment not found
          schema:
            $ref: '#/definitions/app.APIResponse'
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/app.APIResponse'
      security:
      - BearerAuth: []
      summary: Delete deployment
      tags:
      - deployments
    get:
      description: Retrieves details of a specific deployment by name
      parameters:
      - description: Deployment name
        in: path
        name: name
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: Deployment details retrieved successfully
          schema:
            $ref: '#/definitions/app.DeploymentResponse'
        "400":
          description: Invalid request
          schema:
            $ref: '#/definitions/app.APIResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/app.APIResponse'
        "404":
          description: Deployment not found
          schema:
            $ref: '#/definitions/app.APIResponse'
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/app.APIResponse'
      security:
      - BearerAuth: []
      summary: Get deployment
      tags:
      - deployments
  /deployments/{name}/kubeconfig:
    get:
      description: Retrieves the kubeconfig file for a specific deployment
      parameters:
      - description: Deployment name
        in: path
        name: name
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: Kubeconfig retrieved successfully
          schema:
            $ref: '#/definitions/app.KubeconfigResponse'
        "400":
          description: Invalid request
          schema:
            $ref: '#/definitions/app.APIResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/app.APIResponse'
        "404":
          description: Deployment not found
          schema:
            $ref: '#/definitions/app.APIResponse'
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/app.APIResponse'
      security:
      - BearerAuth: []
      summary: Get kubeconfig
      tags:
      - deployments
  /deployments/{name}/nodes:
    post:
      consumes:
      - application/json
      description: Adds a new node to an existing deployment
      parameters:
      - description: Cluster configuration with new node
        in: body
        name: cluster
        required: true
        schema:
          $ref: '#/definitions/app.ClusterInput'
      produces:
      - application/json
      responses:
        "202":
          description: Node addition workflow started successfully
          schema:
            $ref: '#/definitions/app.Response'
        "400":
          description: Invalid request format
          schema:
            $ref: '#/definitions/app.APIResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/app.APIResponse'
        "404":
          description: Deployment not found
          schema:
            $ref: '#/definitions/app.APIResponse'
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/app.APIResponse'
      security:
      - BearerAuth: []
      summary: Add node to deployment
      tags:
      - deployments
  /deployments/{name}/nodes/{node_name}:
    delete:
      description: Removes a specific node from an existing deployment
      parameters:
      - description: Deployment name
        in: path
        name: name
        required: true
        type: string
      - description: Node name to remove
        in: path
        name: node_name
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: Node removal workflow started successfully
          schema:
            $ref: '#/definitions/app.Response'
        "400":
          description: Invalid request
          schema:
            $ref: '#/definitions/app.APIResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/app.APIResponse'
        "404":
          description: Deployment not found
          schema:
            $ref: '#/definitions/app.APIResponse'
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/app.APIResponse'
      security:
      - BearerAuth: []
      summary: Remove node from deployment
      tags:
      - deployments
  /invoices:
    get:
      consumes:
      - application/json
      description: Returns a list of all invoices
      operationId: get-all-invoices
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            items:
              $ref: '#/definitions/models.Invoice'
            type: array
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/app.APIResponse'
      security:
      - AdminMiddleware: []
      summary: Get all invoices
      tags:
      - admin
  /pending-records:
    get:
      consumes:
      - application/json
      description: Returns all pending records in the system
      operationId: list-pending-records
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            items:
              $ref: '#/definitions/app.PendingRecordsResponse'
            type: array
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/app.APIResponse'
      security:
      - AdminMiddleware: []
      summary: List pending records
      tags:
      - admin
  /stats:
    get:
      consumes:
      - application/json
      description: Retrieves comprehensive system statistics.
      operationId: get-stats
      produces:
      - application/json
      responses:
        "200":
          description: System statistics retrieved successfully
          schema:
            $ref: '#/definitions/app.Stats'
        "500":
          description: Internal Server Error - Failed to retrieve statistics
          schema:
            $ref: '#/definitions/app.APIResponse'
      security:
      - AdminMiddleware: []
      summary: Get system statistics
      tags:
      - admin
  /system/maintenance/status:
    get:
      consumes:
      - application/json
      description: Gets maintenance mode for the system
      operationId: get-maintenance-mode
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/app.APIResponse'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/app.APIResponse'
      security:
      - AdminMiddleware: []
      summary: Get maintenance mode
      tags:
      - admin
    put:
      consumes:
      - application/json
      description: Sets maintenance mode for the system
      operationId: set-maintenance-mode
      parameters:
      - description: Maintenance Mode Status
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/app.MaintenanceModeStatus'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/app.APIResponse'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/app.APIResponse'
      security:
      - AdminMiddleware: []
      summary: Set maintenance mode
      tags:
      - admin
  /twins/{twin_id}/account:
    get:
      consumes:
      - application/json
      description: Retrieve the account ID associated with a specific twin ID
      parameters:
      - description: Twin ID
        in: path
        name: twin_id
        required: true
        type: integer
      - description: Pagination limit
        in: query
        name: limit
        type: integer
      - description: Pagination offset
        in: query
        name: offset
        type: integer
      - description: Other optional filter params
        in: query
        name: filterParam
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: Account ID is retrieved successfully
          schema:
            $ref: '#/definitions/app.TwinResponse'
        "400":
          description: Bad Request or Invalid params
          schema:
            $ref: '#/definitions/app.APIResponse'
        "404":
          description: Twin ID not found
          schema:
            $ref: '#/definitions/app.APIResponse'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/app.APIResponse'
      summary: Get account ID by twin ID
      tags:
      - twins
  /user:
    get:
      description: Retrieves all data of the user
      operationId: get-user
      produces:
      - application/json
      responses:
        "200":
          description: User is retrieved successfully
          schema:
            $ref: '#/definitions/app.GetUserResponse'
        "404":
          description: User is not found
          schema:
            $ref: '#/definitions/app.APIResponse'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/app.APIResponse'
      summary: Get user details
      tags:
      - users
  /user/balance:
    get:
      description: Retrieves the user's balance in USD
      operationId: get-user-balance
      produces:
      - application/json
      responses:
        "200":
          description: Balance fetched successfully
          schema:
            $ref: '#/definitions/app.UserBalanceResponse'
        "404":
          description: User is not found
          schema:
            $ref: '#/definitions/app.APIResponse'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/app.APIResponse'
      summary: Get user balance
      tags:
      - users
  /user/balance/charge:
    post:
      consumes:
      - application/json
      description: Charges the user's balance using a payment method
      operationId: charge-balance
      parameters:
      - description: Charge Balance Input
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/app.ChargeBalanceInput'
      produces:
      - application/json
      responses:
        "202":
          description: 'workflow_id: string, email: string'
          schema:
            $ref: '#/definitions/app.ChargeBalanceResponse'
        "400":
          description: Invalid request format or amount
          schema:
            $ref: '#/definitions/app.APIResponse'
        "404":
          description: User is not found
          schema:
            $ref: '#/definitions/app.APIResponse'
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/app.APIResponse'
      summary: Charge user balance
      tags:
      - users
  /user/change_password:
    put:
      consumes:
      - application/json
      description: Changes the user's password
      operationId: change-password
      parameters:
      - description: Change Password Input
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/app.ChangePasswordInput'
      produces:
      - application/json
      responses:
        "202":
          description: Password updated successfully
          schema:
            $ref: '#/definitions/app.APIResponse'
        "400":
          description: Invalid request format or password mismatch
          schema:
            $ref: '#/definitions/app.APIResponse'
        "404":
          description: User is not found
          schema:
            $ref: '#/definitions/app.APIResponse'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/app.APIResponse'
      summary: Change password
      tags:
      - users
  /user/forgot_password:
    post:
      consumes:
      - application/json
      description: Sends a verification code to the user's email for password reset
      operationId: forgot-password
      parameters:
      - description: Email Input
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/app.EmailInput'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/app.RegisterResponse'
        "400":
          description: Invalid request format
          schema:
            $ref: '#/definitions/app.APIResponse'
        "404":
          description: User is not found
          schema:
            $ref: '#/definitions/app.APIResponse'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/app.APIResponse'
      summary: Forgot password
      tags:
      - users
  /user/forgot_password/verify:
    post:
      consumes:
      - application/json
      description: Verifies the code sent to the user's email for password reset
      operationId: verify-forgot-password-code
      parameters:
      - description: Verify Code Input
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/app.VerifyCodeInput'
      produces:
      - application/json
      responses:
        "201":
          description: Verification successful
          schema:
            $ref: '#/definitions/internal.TokenPair'
        "400":
          description: Invalid request format or verification failed
          schema:
            $ref: '#/definitions/app.APIResponse'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/app.APIResponse'
      summary: Verify forgot password code
      tags:
      - users
  /user/invoice:
    get:
      consumes:
      - application/json
      description: Returns a list of invoices for a user
      operationId: get-invoices
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            items:
              $ref: '#/definitions/models.Invoice'
            type: array
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/app.APIResponse'
      security:
      - UserMiddleware: []
      summary: Get invoices
      tags:
      - invoices
  /user/invoice/{invoice_id}:
    get:
      consumes:
      - application/json
      description: Downloads an invoice by ID
      operationId: download-invoice
      parameters:
      - description: Invoice ID
        in: path
        name: invoice_id
        required: true
        type: string
      produces:
      - application/octet-stream
      responses:
        "200":
          description: OK
          schema:
            type: file
        "404":
          description: Invoice is not found
          schema:
            $ref: '#/definitions/app.APIResponse'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/app.APIResponse'
      security:
      - UserMiddleware: []
      summary: Download invoice
      tags:
      - invoices
  /user/login:
    post:
      consumes:
      - application/json
      description: Logs a user in. Checks KYC verification status and updates user
        sponsorship status if needed. Login is not blocked by KYC errors.
      operationId: login-user
      parameters:
      - description: Login Input
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/app.LoginInput'
      produces:
      - application/json
      responses:
        "201":
          description: Created
          schema:
            $ref: '#/definitions/internal.TokenPair'
        "400":
          description: Invalid request format
          schema:
            $ref: '#/definitions/app.APIResponse'
        "401":
          description: Login failed
          schema:
            $ref: '#/definitions/app.APIResponse'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/app.APIResponse'
      summary: Login user (KYC verification checked)
      tags:
      - users
  /user/nodes:
    get:
      consumes:
      - application/json
      description: List nodes from proxy [rented nodes first + randomized shared nodes]
      operationId: list-nodes
      parameters:
      - description: 'Filter by healthy nodes (default: true)'
        in: query
        name: healthy
        type: boolean
      - description: 'Filter by rentable nodes (default: true)'
        in: query
        name: rentable
        type: boolean
      - description: 'Limit the number of nodes returned (default: 50)'
        in: query
        name: limit
        type: integer
      - description: 'Offset for pagination (default: 0)'
        in: query
        name: offset
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: Nodes are retrieved successfully
          schema:
            $ref: '#/definitions/app.APIResponse'
        "400":
          description: Invalid filter parameters
          schema:
            $ref: '#/definitions/app.APIResponse'
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/app.APIResponse'
      security:
      - UserMiddleware: []
      summary: List nodes
      tags:
      - nodes
  /user/nodes/{node_id}:
    post:
      consumes:
      - application/json
      description: Reserves a node for a user
      operationId: reserve-node
      parameters:
      - description: Node ID
        in: path
        name: node_id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "202":
          description: Accepted
          schema:
            $ref: '#/definitions/app.ReserveNodeResponse'
        "400":
          description: Invalid request
          schema:
            $ref: '#/definitions/app.APIResponse'
        "404":
          description: No nodes are available for rent.
          schema:
            $ref: '#/definitions/app.APIResponse'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/app.APIResponse'
      security:
      - UserMiddleware: []
      summary: Reserve node
      tags:
      - nodes
  /user/nodes/rentable:
    get:
      consumes:
      - application/json
      description: Retrieves a list of rentable nodes from the grid proxy. These are
        healthy nodes that are available for rent.
      operationId: list-rentable-nodes
      produces:
      - application/json
      responses:
        "200":
          description: Rentable nodes retrieved successfully
          schema:
            allOf:
            - $ref: '#/definitions/app.APIResponse'
            - properties:
                data:
                  $ref: '#/definitions/app.ListNodesWithDiscountResponse'
              type: object
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/app.APIResponse'
      summary: List rentable nodes
      tags:
      - nodes
  /user/nodes/rented:
    get:
      consumes:
      - application/json
      description: Returns a list of reserved nodes for a user
      operationId: list-reserved-nodes
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/app.APIResponse'
            - properties:
                data:
                  $ref: '#/definitions/app.ListNodesWithDiscountResponse'
              type: object
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/app.APIResponse'
      security:
      - UserMiddleware: []
      summary: List reserved nodes
      tags:
      - nodes
  /user/nodes/unreserve/{contract_id}:
    delete:
      consumes:
      - application/json
      description: Unreserve a node for a user
      operationId: unreserve-node
      parameters:
      - description: Contract ID
        in: path
        name: contract_id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "202":
          description: Accepted
          schema:
            $ref: '#/definitions/app.UnreserveNodeResponse'
        "400":
          description: Invalid request
          schema:
            $ref: '#/definitions/app.APIResponse'
        "404":
          description: User is not found
          schema:
            $ref: '#/definitions/app.APIResponse'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/app.APIResponse'
      security:
      - UserMiddleware: []
      summary: Unreserve node
      tags:
      - nodes
  /user/pending-records:
    get:
      consumes:
      - application/json
      description: Returns user pending records in the system
      operationId: list-user-pending-records
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            items:
              $ref: '#/definitions/app.PendingRecordsResponse'
            type: array
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/app.APIResponse'
      security:
      - BearerAuth: []
      summary: List user pending records
      tags:
      - users
  /user/redeem/{voucher_code}:
    put:
      description: Redeems a voucher for the user
      operationId: redeem-voucher
      parameters:
      - description: Voucher Code
        in: path
        name: voucher_code
        required: true
        type: string
      produces:
      - application/json
      responses:
        "202":
          description: 'workflow_id: string, voucher_code: string, amount: float64,
            email: string'
          schema:
            $ref: '#/definitions/app.RedeemVoucherResponse'
        "400":
          description: Invalid voucher code, already redeemed, or expired
          schema:
            $ref: '#/definitions/app.APIResponse'
        "404":
          description: User or voucher are not found
          schema:
            $ref: '#/definitions/app.APIResponse'
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/app.APIResponse'
      summary: Redeem voucher
      tags:
      - users
  /user/refresh:
    post:
      consumes:
      - application/json
      description: Refreshes the access token using a valid refresh token
      operationId: refresh-token
      parameters:
      - description: Refresh Token Input
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/app.RefreshTokenInput'
      produces:
      - application/json
      responses:
        "201":
          description: Created
          schema:
            $ref: '#/definitions/app.RefreshTokenResponse'
        "400":
          description: Invalid request format
          schema:
            $ref: '#/definitions/app.APIResponse'
        "401":
          description: Invalid or expired refresh token
          schema:
            $ref: '#/definitions/app.APIResponse'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/app.APIResponse'
      summary: Refresh access token
      tags:
      - users
  /user/register:
    post:
      consumes:
      - application/json
      description: Registers a new user, sets up blockchain account, and creates KYC
        sponsorship. Sends verification code to email.
      operationId: register-user
      parameters:
      - description: Register Input
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/app.RegisterInput'
      produces:
      - application/json
      responses:
        "201":
          description: 'workflow_id: string, email: string'
          schema:
            $ref: '#/definitions/app.RegisterUserResponse'
        "400":
          description: Invalid request format
          schema:
            $ref: '#/definitions/app.APIResponse'
        "409":
          description: User is already registered
          schema:
            $ref: '#/definitions/app.APIResponse'
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/app.APIResponse'
      summary: Register user (with KYC sponsorship)
      tags:
      - users
  /user/register/verify:
    post:
      consumes:
      - application/json
      description: Verifies the email using the registration code
      operationId: verify-register-code
      parameters:
      - description: Verification details
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/app.VerifyCodeInput'
      produces:
      - application/json
      responses:
        "201":
          description: Created
          schema:
            $ref: '#/definitions/app.VerifyRegisterUserResponse'
        "400":
          description: Invalid request
          schema:
            $ref: '#/definitions/app.APIResponse'
        "409":
          description: User is already registered
          schema:
            $ref: '#/definitions/app.APIResponse'
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/app.APIResponse'
      summary: Verify registration code
      tags:
      - users
  /user/ssh-keys:
    get:
      consumes:
      - application/json
      description: Lists all SSH keys for the authenticated user
      operationId: list-ssh-keys
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            items:
              $ref: '#/definitions/models.SSHKey'
            type: array
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/app.APIResponse'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/app.APIResponse'
      security:
      - BearerAuth: []
      summary: List user SSH keys
      tags:
      - users
    post:
      consumes:
      - application/json
      description: Adds a new SSH key for the authenticated user
      operationId: add-ssh-key
      parameters:
      - description: SSH Key Input
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/app.SSHKeyInput'
      produces:
      - application/json
      responses:
        "201":
          description: Created
          schema:
            $ref: '#/definitions/models.SSHKey'
        "400":
          description: Invalid request format
          schema:
            $ref: '#/definitions/app.APIResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/app.APIResponse'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/app.APIResponse'
      security:
      - BearerAuth: []
      summary: Add SSH key
      tags:
      - users
  /user/ssh-keys/{ssh_key_id}:
    delete:
      consumes:
      - application/json
      description: Deletes an SSH key for the authenticated user
      operationId: delete-ssh-key
      parameters:
      - description: SSH Key ID
        in: path
        name: ssh_key_id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/app.APIResponse'
        "400":
          description: Invalid SSH key ID
          schema:
            $ref: '#/definitions/app.APIResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/app.APIResponse'
        "404":
          description: SSH key not found
          schema:
            $ref: '#/definitions/app.APIResponse'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/app.APIResponse'
      security:
      - BearerAuth: []
      summary: Delete SSH key
      tags:
      - users
  /users:
    get:
      consumes:
      - application/json
      description: Returns a list of all users
      operationId: get-all-users
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            items:
              $ref: '#/definitions/app.UserResponse'
            type: array
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/app.APIResponse'
      security:
      - AdminMiddleware: []
      summary: Get all users
      tags:
      - admin
  /users/{user_id}:
    delete:
      consumes:
      - application/json
      description: Deletes a user from the system
      operationId: delete-user
      parameters:
      - description: User ID
        in: path
        name: user_id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/app.APIResponse'
        "400":
          description: Invalid user ID
          schema:
            $ref: '#/definitions/app.APIResponse'
        "403":
          description: Admins cannot delete their own account
          schema:
            $ref: '#/definitions/app.APIResponse'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/app.APIResponse'
      security:
      - AdminMiddleware: []
      summary: Delete a user
      tags:
      - admin
  /users/{user_id}/credit:
    post:
      consumes:
      - application/json
      description: Credits a specific user's balance
      operationId: credit-user
      parameters:
      - description: User ID
        in: path
        name: user_id
        required: true
        type: string
      - description: Credit Request Input
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/app.CreditRequestInput'
      produces:
      - application/json
      responses:
        "201":
          description: Created
          schema:
            $ref: '#/definitions/app.CreditUserResponse'
        "400":
          description: Invalid request format or user ID
          schema:
            $ref: '#/definitions/app.APIResponse'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/app.APIResponse'
      security:
      - AdminMiddleware: []
      summary: Credit user balance
      tags:
      - admin
  /users/mail:
    post:
      consumes:
      - multipart/form-data
      description: Allows admin to send a custom email to all users with optional
        file attachments. Returns detailed statistics about successful and failed
        email deliveries.
      operationId: admin-mail-all-users
      parameters:
      - description: Email subject
        in: formData
        name: subject
        required: true
        type: string
      - description: Email body content
        in: formData
        name: body
        required: true
        type: string
      - description: Email attachments (multiple files allowed)
        in: formData
        name: attachments
        type: file
      produces:
      - application/json
      responses:
        "200":
          description: Email sending results with delivery statistics
          schema:
            allOf:
            - $ref: '#/definitions/app.APIResponse'
            - properties:
                data:
                  $ref: '#/definitions/app.SendMailResponse'
              type: object
        "400":
          description: Invalid request format
          schema:
            $ref: '#/definitions/app.APIResponse'
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/app.APIResponse'
      security:
      - AdminMiddleware: []
      summary: Send mail to all users
      tags:
      - admin
  /vouchers:
    get:
      consumes:
      - application/json
      description: Returns all vouchers in the system
      operationId: list-vouchers
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            items:
              $ref: '#/definitions/models.Voucher'
            type: array
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/app.APIResponse'
      security:
      - AdminMiddleware: []
      summary: List vouchers
      tags:
      - admin
  /vouchers/generate:
    post:
      consumes:
      - application/json
      description: Generates a bulk of vouchers
      operationId: generate-vouchers
      parameters:
      - description: Generate Vouchers Input
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/app.GenerateVouchersInput'
      produces:
      - application/json
      responses:
        "201":
          description: Created
          schema:
            items:
              $ref: '#/definitions/models.Voucher'
            type: array
        "400":
          description: Invalid request format
          schema:
            $ref: '#/definitions/app.APIResponse'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/app.APIResponse'
      security:
      - AdminMiddleware: []
      summary: Generate vouchers
      tags:
      - admin
  /workflow/{workflow_id}:
    get:
      consumes:
      - application/json
      description: Returns the status of a workflow by its ID.
      operationId: get-workflow-status
      parameters:
      - description: Workflow ID
        in: path
        name: workflow_id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: Workflow status returned successfully
          schema:
            type: string
        "400":
          description: Invalid request or missing workflow ID
          schema:
            $ref: '#/definitions/app.APIResponse'
        "404":
          description: Workflow not found
          schema:
            $ref: '#/definitions/app.APIResponse'
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/app.APIResponse'
      summary: Get workflow status
      tags:
      - workflow
swagger: "2.0"
